[2025-07-08 18:58:30] local.ERROR: The provided cwd "/Users/<USER>/Herd/ehigold/public" does not exist. {"exception":"[object] (Symfony\\Component\\Process\\Exception\\RuntimeException(code: 0): The provided cwd \"/Users/<USER>/Herd/ehigold/public\" does not exist. at /Users/<USER>/Herd/ehigold/vendor/symfony/process/Process.php:341)
[stacktrace]
#0 /Users/<USER>/Herd/ehigold/vendor/laravel/framework/src/Illuminate/Foundation/Console/ServeCommand.php(190): Symfony\\Component\\Process\\Process->start(Object(Closure))
#1 /Users/<USER>/Herd/ehigold/vendor/laravel/framework/src/Illuminate/Foundation/Console/ServeCommand.php(129): Illuminate\\Foundation\\Console\\ServeCommand->startProcess(true)
#2 /Users/<USER>/Herd/ehigold/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Console\\ServeCommand->handle()
#3 /Users/<USER>/Herd/ehigold/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#4 /Users/<USER>/Herd/ehigold/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 /Users/<USER>/Herd/ehigold/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 /Users/<USER>/Herd/ehigold/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#7 /Users/<USER>/Herd/ehigold/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call(Array)
#8 /Users/<USER>/Herd/ehigold/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 /Users/<USER>/Herd/ehigold/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#10 /Users/<USER>/Herd/ehigold/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 /Users/<USER>/Herd/ehigold/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\ServeCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 /Users/<USER>/Herd/ehigold/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 /Users/<USER>/Herd/ehigold/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 /Users/<USER>/Herd/ehigold/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 /Users/<USER>/Herd/ehigold/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#16 {main}
"} 
[2025-07-08 19:00:27] local.ERROR: Uncaught InvalidArgumentException: Please provide a valid cache path. in /Users/<USER>/Herd/EHIGOLD/vendor/laravel/framework/src/Illuminate/View/Compilers/Compiler.php:67
Stack trace:
#0 /Users/<USER>/Herd/EHIGOLD/vendor/laravel/framework/src/Illuminate/View/ViewServiceProvider.php(97): Illuminate\View\Compilers\Compiler->__construct(Object(Illuminate\Filesystem\Filesystem), false, '', true, 'php')
#1 /Users/<USER>/Herd/EHIGOLD/vendor/laravel/framework/src/Illuminate/Container/Container.php(1010): Illuminate\View\ViewServiceProvider->{closure:Illuminate\View\ViewServiceProvider::registerBladeCompiler():96}(Object(Illuminate\Foundation\Application), Array)
#2 /Users/<USER>/Herd/EHIGOLD/vendor/laravel/framework/src/Illuminate/Container/Container.php(890): Illuminate\Container\Container->build(Object(Closure))
#3 /Users/<USER>/Herd/EHIGOLD/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1078): Illuminate\Container\Container->resolve('blade.compiler', Array, true)
#4 /Users/<USER>/Herd/EHIGOLD/vendor/laravel/framework/src/Illuminate/Container/Container.php(821): Illuminate\Foundation\Application->resolve('blade.compiler', Array)
#5 /Users/<USER>/Herd/EHIGOLD/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1058): Illuminate\Container\Container->make('blade.compiler', Array)
#6 /Users/<USER>/Herd/EHIGOLD/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(124): Illuminate\Foundation\Application->make('blade.compiler', Array)
#7 Command line code(1): app('Illuminate\\View...')
#8 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught InvalidArgumentException: Please provide a valid cache path. in /Users/<USER>/Herd/EHIGOLD/vendor/laravel/framework/src/Illuminate/View/Compilers/Compiler.php:67
Stack trace:
#0 /Users/<USER>/Herd/EHIGOLD/vendor/laravel/framework/src/Illuminate/View/ViewServiceProvider.php(97): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), false, '', true, 'php')
#1 /Users/<USER>/Herd/EHIGOLD/vendor/laravel/framework/src/Illuminate/Container/Container.php(1010): Illuminate\\View\\ViewServiceProvider->{closure:Illuminate\\View\\ViewServiceProvider::registerBladeCompiler():96}(Object(Illuminate\\Foundation\\Application), Array)
#2 /Users/<USER>/Herd/EHIGOLD/vendor/laravel/framework/src/Illuminate/Container/Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 /Users/<USER>/Herd/EHIGOLD/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1078): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 /Users/<USER>/Herd/EHIGOLD/vendor/laravel/framework/src/Illuminate/Container/Container.php(821): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 /Users/<USER>/Herd/EHIGOLD/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1058): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 /Users/<USER>/Herd/EHIGOLD/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(124): Illuminate\\Foundation\\Application->make('blade.compiler', Array)
#7 Command line code(1): app('Illuminate\\\\View...')
#8 {main}
  thrown at /Users/<USER>/Herd/EHIGOLD/vendor/laravel/framework/src/Illuminate/View/Compilers/Compiler.php:67)
[stacktrace]
#0 {main}
"} 
